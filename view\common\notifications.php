<?php
// Vérifier l'authentification pour tous les rôles
session_start();

// Vérifier si l'utilisateur est connecté
if (!isset($_SESSION['user'])) {
    header('Location: ../../auth/login.php');
    exit();
}

// Récupérer les informations de l'utilisateur
$userRole = $_SESSION['user']['role'] ?? '';
$userId = $_SESSION['user']['id'] ?? $_SESSION['user']['id_user'] ?? null;
$userName = $_SESSION['user']['nom'] ?? $_SESSION['user']['username'] ?? 'Utilisateur';

// Configuration de la page
$pageTitle = "Notifications";
$currentPage = "notifications";

require_once __DIR__ . '/../../config/db.php';
require_once __DIR__ . '/../../model/notificationsModel.php';
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - ENSAH</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/messages.css">

    <style>
        .notifications-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding: 1rem 0;
            border-bottom: 2px solid #e9ecef;
        }

        .notifications-header h1 {
            color: #2c3e50;
            font-weight: 600;
            margin: 0;
        }

        .notifications-actions {
            display: flex;
            gap: 1rem;
        }

        .create-notification-btn, .mark-all-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .create-notification-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .create-notification-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .mark-all-btn {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .mark-all-btn:hover {
            background: #e9ecef;
            color: #495057;
        }

        .role-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            margin-left: 1rem;
        }

        .role-admin { background: #dc3545; color: white; }
        .role-chef { background: #28a745; color: white; }
        .role-coordinateur { background: #17a2b8; color: white; }
        .role-enseignant { background: #ffc107; color: #212529; }
        .role-vacataire { background: #6f42c1; color: white; }
        .role-etudiant { background: #fd7e14; color: white; }

        .notification-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .notification-item:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .notification-item.unread {
            border-left: 4px solid #667eea;
            background: #f8f9ff;
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
        }

        .notification-icon.info {
            background: #e3f2fd;
            color: #1976d2;
        }

        .notification-icon.warning {
            background: #fff3e0;
            color: #f57c00;
        }

        .notification-icon.success {
            background: #e8f5e8;
            color: #388e3c;
        }

        .notification-icon.error {
            background: #ffebee;
            color: #d32f2f;
        }
    </style>
</head>
<body>
    <div class="dashboards-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid p-4">
                <div class="notifications-header">
                    <div>
                        <h1>
                            <i class="fas fa-bell me-3"></i>
                            Notifications
                        </h1>
                        <span class="role-badge role-<?php echo str_replace(' ', '-', strtolower($userRole)); ?>">
                            <?php echo ucfirst($userRole); ?>
                        </span>
                    </div>
                    <div class="notifications-actions">
                        <?php if (in_array($userRole, ['admin'])): ?>
                        <button id="createNotificationBtn" class="create-notification-btn">
                            <i class="fas fa-plus me-1"></i>
                            Créer une notification
                        </button>
                        <?php endif; ?>
                        <button id="markAllBtn" class="mark-all-btn">
                            <i class="fas fa-check-double me-1"></i>
                            Tout marquer comme lu
                        </button>
                    </div>
                </div>

                <div id="notificationsList" class="notifications-list">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                        <p class="mt-2 text-muted">Chargement des notifications...</p>
                    </div>
                </div>

                <!-- Pagination will be added here by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Notification Creation Modal -->
    <?php if (in_array($userRole, ['admin'])): ?>
    <div class="modal fade" id="createNotificationModal" tabindex="-1" aria-labelledby="createNotificationModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createNotificationModalLabel">
                        <i class="fas fa-bell me-2"></i>
                        Créer une nouvelle notification
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="createNotificationForm">
                        <div class="mb-3">
                            <label for="notificationTitle" class="form-label">Titre de la notification</label>
                            <input type="text" class="form-control" id="notificationTitle" required>
                        </div>
                        <div class="mb-3">
                            <label for="notificationMessage" class="form-label">Message</label>
                            <textarea class="form-control" id="notificationMessage" rows="5" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="notificationType" class="form-label">Type de notification</label>
                            <select class="form-select" id="notificationType">
                                <option value="info">Information</option>
                                <option value="warning">Avertissement</option>
                                <option value="success">Succès</option>
                                <option value="error">Erreur</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="createNotification()">
                        <i class="fas fa-paper-plane me-1"></i>
                        Publier
                    </button>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Configuration pour le JavaScript
        const userRole = '<?php echo $userRole; ?>';
        const userId = <?php echo $userId ? $userId : 'null'; ?>;

        // Initialiser la page
        document.addEventListener('DOMContentLoaded', function() {
            loadNotifications();
            
            // Event listeners pour les boutons
            const createBtn = document.getElementById('createNotificationBtn');
            if (createBtn) {
                createBtn.addEventListener('click', function() {
                    const modal = new bootstrap.Modal(document.getElementById('createNotificationModal'));
                    modal.show();
                });
            }

            const markAllBtn = document.getElementById('markAllBtn');
            if (markAllBtn) {
                markAllBtn.addEventListener('click', markAllNotificationsAsRead);
            }
        });

        // Fonction pour charger les notifications
        function loadNotifications() {
            fetch('../../route/notificationsRoute.php?action=getAll')
                .then(response => response.json())
                .then(data => {
                    displayNotifications(data.notifications || []);
                })
                .catch(error => {
                    console.error('Error loading notifications:', error);
                    document.getElementById('notificationsList').innerHTML = 
                        '<div class="alert alert-danger">Erreur lors du chargement des notifications.</div>';
                });
        }

        // Fonction pour afficher les notifications
        function displayNotifications(notifications) {
            const container = document.getElementById('notificationsList');
            
            if (notifications.length === 0) {
                container.innerHTML = '<div class="text-center py-4"><p class="text-muted">Aucune notification trouvée.</p></div>';
                return;
            }

            let html = '';
            notifications.forEach(notification => {
                const iconClass = getNotificationIcon(notification.type);
                html += `
                    <div class="notification-item ${notification.is_read ? '' : 'unread'}">
                        <div class="d-flex">
                            <div class="notification-icon ${notification.type}">
                                <i class="${iconClass}"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">${notification.title}</h6>
                                <p class="mb-2">${notification.message}</p>
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    ${new Date(notification.created_at).toLocaleString('fr-FR')}
                                </small>
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // Fonction pour obtenir l'icône selon le type
        function getNotificationIcon(type) {
            switch(type) {
                case 'warning': return 'fas fa-exclamation-triangle';
                case 'success': return 'fas fa-check-circle';
                case 'error': return 'fas fa-times-circle';
                default: return 'fas fa-info-circle';
            }
        }

        // Fonction pour créer une notification (admin seulement)
        function createNotification() {
            const title = document.getElementById('notificationTitle').value.trim();
            const message = document.getElementById('notificationMessage').value.trim();
            const type = document.getElementById('notificationType').value;

            if (!title || !message) {
                alert('Veuillez remplir tous les champs obligatoires.');
                return;
            }

            fetch('../../route/notificationsRoute.php?action=create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    title: title,
                    message: message,
                    type: type
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Notification créée avec succès !');
                    const modal = bootstrap.Modal.getInstance(document.getElementById('createNotificationModal'));
                    modal.hide();
                    document.getElementById('createNotificationForm').reset();
                    loadNotifications(); // Recharger les notifications
                } else {
                    alert('Erreur lors de la création de la notification.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Erreur lors de la création de la notification.');
            });
        }

        // Fonction pour marquer toutes les notifications comme lues
        function markAllNotificationsAsRead() {
            // Cette fonction sera implémentée selon le modèle de notifications
            alert('Fonctionnalité en cours de développement.');
        }
    </script>
</body>
</html>
