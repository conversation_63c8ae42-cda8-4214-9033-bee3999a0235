<?php
// Vérifier l'authentification pour tous les rôles
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Vérifier si l'utilisateur est connecté
if (!isset($_SESSION['user'])) {
    header('Location: ../../auth/login.php');
    exit();
}

// Récupérer les informations de l'utilisateur
$userRole = $_SESSION['user']['role'] ?? '';
$userId = $_SESSION['user']['id'] ?? $_SESSION['user']['id_user'] ?? null;
$userName = $_SESSION['user']['nom'] ?? $_SESSION['user']['username'] ?? 'Utilisateur';

// Configuration de la page
$pageTitle = "Messages";
$currentPage = "messages";

require_once __DIR__ . '/../../config/db.php';
require_once __DIR__ . '/../../model/messagesModel.php';
require_once __DIR__ . '/../../controller/messagesController.php';

// Get unread count
$unreadCount = getUnreadCount();
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - ENSAH</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/messages.css">

    <style>
        .messages-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding: 1rem 0;
            border-bottom: 2px solid #e9ecef;
        }

        .messages-header h1 {
            color: #2c3e50;
            font-weight: 600;
            margin: 0;
        }

        .messages-actions {
            display: flex;
            gap: 1rem;
        }

        .create-message-btn, .mark-all-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .create-message-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .create-message-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .mark-all-btn {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .mark-all-btn:hover {
            background: #e9ecef;
            color: #495057;
        }

        .role-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            margin-left: 1rem;
        }

        .role-admin { background: #dc3545; color: white; }
        .role-chef { background: #28a745; color: white; }
        .role-coordinateur { background: #17a2b8; color: white; }
        .role-enseignant { background: #ffc107; color: #212529; }
        .role-vacataire { background: #6f42c1; color: white; }
        .role-etudiant { background: #fd7e14; color: white; }

        /* Styles pour les messages */
        .message-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .message-item:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .message-item.unread {
            border-left: 4px solid #667eea;
            background: #f8f9ff;
        }

        .message-item.message-received {
            border-left-color: #28a745;
        }

        .message-item.message-sent {
            border-left-color: #17a2b8;
        }

        .message-item.message-general {
            border-left-color: #ffc107;
        }

        .message-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            background: #f8f9fa;
            color: #6c757d;
        }

        .message-received .message-icon {
            background: #d4edda;
            color: #28a745;
        }

        .message-sent .message-icon {
            background: #d1ecf1;
            color: #17a2b8;
        }

        .message-general .message-icon {
            background: #fff3cd;
            color: #ffc107;
        }

        .message-meta {
            font-size: 0.875rem;
        }

        .pagination-container {
            margin-top: 2rem;
        }

        .pagination .page-link {
            color: #667eea;
            border-color: #dee2e6;
        }

        .pagination .page-item.active .page-link {
            background-color: #667eea;
            border-color: #667eea;
        }

        .pagination .page-link:hover {
            color: #5a6fd8;
            background-color: #f8f9ff;
            border-color: #dee2e6;
        }
    </style>
</head>
<body>
    <div class="dashboards-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid p-4">
                <div class="messages-header">
                    <div>
                        <h1>
                            <i class="fas fa-envelope me-3"></i>
                            Messages
                        </h1>
                        <span class="role-badge role-<?php echo str_replace(' ', '-', strtolower($userRole)); ?>">
                            <?php echo ucfirst($userRole); ?>
                        </span>
                    </div>
                    <div class="messages-actions">
                        <?php if (in_array($userRole, ['admin', 'chef de departement'])): ?>
                        <button id="createMessageBtn" class="create-message-btn">
                            <i class="fas fa-plus me-1"></i>
                            Créer un message
                        </button>
                        <?php endif; ?>
                        <button id="markAllBtn" class="mark-all-btn">
                            <i class="fas fa-check-double me-1"></i>
                            Tout marquer comme lu
                        </button>
                    </div>
                </div>

                <div id="notificationsList" class="notifications-list">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                        <p class="mt-2 text-muted">Chargement des messages...</p>
                    </div>
                </div>

                <!-- Pagination will be added here by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Message Creation Modal -->
    <?php if (in_array($userRole, ['admin', 'chef de departement'])): ?>
    <div class="modal fade" id="createMessageModal" tabindex="-1" aria-labelledby="createMessageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createMessageModalLabel">
                        <i class="fas fa-envelope me-2"></i>
                        Créer un nouveau message
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="createMessageForm">
                        <div class="mb-3">
                            <label for="messageTitle" class="form-label">Titre du message</label>
                            <input type="text" class="form-control" id="messageTitle" required>
                        </div>
                        <div class="mb-3">
                            <label for="messageContent" class="form-label">Contenu</label>
                            <textarea class="form-control" id="messageContent" rows="5" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="messageReceiver" class="form-label">Destinataire (optionnel)</label>
                            <select class="form-select" id="messageReceiver">
                                <option value="">Message général (tous les utilisateurs)</option>
                                <!-- Options will be populated by JavaScript -->
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="createMessage()">
                        <i class="fas fa-paper-plane me-1"></i>
                        Envoyer
                    </button>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Messages JS -->
    <script src="../assets/js/messages.js"></script>

    <script>
        // Configuration pour le JavaScript
        const userRole = '<?php echo $userRole; ?>';
        const userId = <?php echo $userId ? $userId : 'null'; ?>;
        const basePath = '../../route/messagesRoute.php';

        // Variables globales pour la pagination
        let currentPage = 1;
        let totalPages = 1;

        // Initialiser la page
        document.addEventListener('DOMContentLoaded', function() {
            loadMessages();

            // Event listeners pour les boutons
            const createBtn = document.getElementById('createMessageBtn');
            if (createBtn) {
                createBtn.addEventListener('click', function() {
                    const modal = new bootstrap.Modal(document.getElementById('createMessageModal'));
                    modal.show();
                });
            }

            const markAllBtn = document.getElementById('markAllBtn');
            if (markAllBtn) {
                markAllBtn.addEventListener('click', markAllAsRead);
            }
        });

        // Fonction pour charger les messages filtrés par utilisateur
        function loadMessages(page = 1) {
            if (!userId) {
                console.error('User ID not available');
                document.getElementById('notificationsList').innerHTML =
                    '<div class="alert alert-danger">Erreur: ID utilisateur non disponible.</div>';
                return;
            }

            currentPage = page;

            // Afficher le spinner de chargement
            document.getElementById('notificationsList').innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-2 text-muted">Chargement des messages...</p>
                </div>
            `;

            fetch(`${basePath}?action=getByUser&user_id=${userId}&page=${page}&perPage=10`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        throw new Error(data.error);
                    }
                    displayMessages(data.messages || []);
                    updatePagination(data.pages || 1, data.current_page || 1);
                    totalPages = data.pages || 1;
                })
                .catch(error => {
                    console.error('Error loading messages:', error);
                    document.getElementById('notificationsList').innerHTML =
                        '<div class="alert alert-danger">Erreur lors du chargement des messages: ' + error.message + '</div>';
                });
        }

        // Fonction pour créer un message
        function createMessage() {
            const title = document.getElementById('messageTitle').value.trim();
            const content = document.getElementById('messageContent').value.trim();
            const receiverId = document.getElementById('messageReceiver').value;

            if (!title || !content) {
                alert('Veuillez remplir tous les champs obligatoires.');
                return;
            }

            const messageData = {
                title: title,
                content: content,
                sender_id: userId
            };

            if (receiverId) {
                messageData.receiver_id = receiverId;
            }

            fetch('../../route/messagesRoute.php?action=create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(messageData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Message envoyé avec succès !');
                    const modal = bootstrap.Modal.getInstance(document.getElementById('createMessageModal'));
                    modal.hide();
                    document.getElementById('createMessageForm').reset();
                    loadMessages(); // Recharger les messages
                } else {
                    alert('Erreur lors de l\'envoi du message.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Erreur lors de l\'envoi du message.');
            });
        }

        // Fonction pour afficher les messages
        function displayMessages(messages) {
            const container = document.getElementById('notificationsList');

            if (messages.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-envelope-open fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucun message trouvé</h5>
                        <p class="text-muted">Vous n'avez aucun message pour le moment.</p>
                    </div>
                `;
                return;
            }

            let html = '';
            messages.forEach(message => {
                const messageTypeClass = getMessageTypeClass(message.message_type);
                const messageTypeIcon = getMessageTypeIcon(message.message_type);
                const isUnread = message.is_read == 0;

                html += `
                    <div class="message-item ${isUnread ? 'unread' : ''} ${messageTypeClass}" data-message-id="${message.id}">
                        <div class="d-flex">
                            <div class="message-icon">
                                <i class="${messageTypeIcon}"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="mb-0 ${isUnread ? 'fw-bold' : ''}">${escapeHtml(message.title)}</h6>
                                    <div class="d-flex align-items-center">
                                        <span class="badge ${getMessageTypeBadgeClass(message.message_type)} me-2">
                                            ${message.type_label}
                                        </span>
                                        ${isUnread ? '<span class="badge bg-primary">Nouveau</span>' : ''}
                                    </div>
                                </div>
                                <p class="mb-2 text-muted">${escapeHtml(message.content)}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        ${formatDate(message.created_at)}
                                    </small>
                                    <div class="message-meta">
                                        ${message.message_type === 'received' ?
                                            `<small class="text-muted">De: ${message.sender_name || 'Système'}</small>` :
                                            message.message_type === 'sent' ?
                                            `<small class="text-muted">À: ${message.receiver_name || 'Tous'}</small>` :
                                            '<small class="text-muted">Message général</small>'
                                        }
                                    </div>
                                </div>
                                ${isUnread ? `
                                    <div class="mt-2">
                                        <button class="btn btn-sm btn-outline-primary" onclick="markMessageAsRead(${message.id})">
                                            <i class="fas fa-check me-1"></i>Marquer comme lu
                                        </button>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // Fonction pour marquer tous les messages comme lus (version utilisateur spécifique)
        function markAllAsRead() {
            if (!userId) {
                alert('Erreur: ID utilisateur non disponible.');
                return;
            }

            fetch('../../route/messagesRoute.php?action=markAllAsReadForUser', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_id: userId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadMessages(currentPage); // Recharger les messages
                    showNotification('Tous les messages ont été marqués comme lus.', 'success');
                } else {
                    showNotification('Erreur lors de la mise à jour.', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Erreur lors de la mise à jour.', 'error');
            });
        }

        // Fonction pour marquer un message spécifique comme lu
        function markMessageAsRead(messageId) {
            fetch('../../route/messagesRoute.php?action=markAsRead', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    id: messageId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadMessages(currentPage); // Recharger les messages
                } else {
                    showNotification('Erreur lors de la mise à jour.', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Erreur lors de la mise à jour.', 'error');
            });
        }

        // Fonctions utilitaires pour l'affichage des messages
        function getMessageTypeClass(messageType) {
            switch(messageType) {
                case 'received': return 'message-received';
                case 'sent': return 'message-sent';
                case 'general': return 'message-general';
                default: return '';
            }
        }

        function getMessageTypeIcon(messageType) {
            switch(messageType) {
                case 'received': return 'fas fa-inbox';
                case 'sent': return 'fas fa-paper-plane';
                case 'general': return 'fas fa-bullhorn';
                default: return 'fas fa-envelope';
            }
        }

        function getMessageTypeBadgeClass(messageType) {
            switch(messageType) {
                case 'received': return 'bg-success';
                case 'sent': return 'bg-info';
                case 'general': return 'bg-warning text-dark';
                default: return 'bg-secondary';
            }
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('fr-FR', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // Fonction pour mettre à jour la pagination
        function updatePagination(totalPages, currentPage) {
            const container = document.getElementById('notificationsList').parentNode;

            // Supprimer l'ancienne pagination
            const existingPagination = container.querySelector('.pagination-container');
            if (existingPagination) {
                existingPagination.remove();
            }

            if (totalPages <= 1) {
                return; // Pas besoin de pagination
            }

            const paginationHtml = `
                <div class="pagination-container mt-4">
                    <nav aria-label="Navigation des messages">
                        <ul class="pagination justify-content-center">
                            <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                                <a class="page-link" href="#" onclick="loadMessages(${currentPage - 1}); return false;">
                                    <i class="fas fa-chevron-left"></i> Précédent
                                </a>
                            </li>
                            ${generatePageNumbers(totalPages, currentPage)}
                            <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                                <a class="page-link" href="#" onclick="loadMessages(${currentPage + 1}); return false;">
                                    Suivant <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            `;

            container.insertAdjacentHTML('beforeend', paginationHtml);
        }

        function generatePageNumbers(totalPages, currentPage) {
            let html = '';
            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

            if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            for (let i = startPage; i <= endPage; i++) {
                html += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="loadMessages(${i}); return false;">${i}</a>
                    </li>
                `;
            }

            return html;
        }

        // Fonction pour afficher les notifications
        function showNotification(message, type = 'info') {
            // Supprimer les notifications existantes
            const existingNotifications = document.querySelectorAll('.notification-toast');
            existingNotifications.forEach(notification => notification.remove());

            // Créer la nouvelle notification
            const notification = document.createElement('div');
            notification.className = `notification-toast alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                max-width: 500px;
            `;

            const icon = type === 'success' ? 'check-circle' :
                        type === 'error' ? 'exclamation-triangle' :
                        'info-circle';

            notification.innerHTML = `
                <i class="fas fa-${icon} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;

            document.body.appendChild(notification);

            // Auto-suppression après 5 secondes
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
