<?php
// Vérifier l'authentification pour tous les rôles
session_start();

// Vérifier si l'utilisateur est connecté
if (!isset($_SESSION['user'])) {
    header('Location: ../../auth/login.php');
    exit();
}

// Récupérer les informations de l'utilisateur
$userRole = $_SESSION['user']['role'] ?? '';
$userId = $_SESSION['user']['id'] ?? $_SESSION['user']['id_user'] ?? null;
$userName = $_SESSION['user']['nom'] ?? $_SESSION['user']['username'] ?? 'Utilisateur';

// Configuration de la page
$pageTitle = "Messages";
$currentPage = "messages";

require_once __DIR__ . '/../../config/db.php';
require_once __DIR__ . '/../../model/messagesModel.php';
require_once __DIR__ . '/../../controller/messagesController.php';

// Get unread count
$unreadCount = getUnreadCount();
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - ENSAH</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/messages.css">

    <style>
        .messages-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding: 1rem 0;
            border-bottom: 2px solid #e9ecef;
        }

        .messages-header h1 {
            color: #2c3e50;
            font-weight: 600;
            margin: 0;
        }

        .messages-actions {
            display: flex;
            gap: 1rem;
        }

        .create-message-btn, .mark-all-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .create-message-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .create-message-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .mark-all-btn {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .mark-all-btn:hover {
            background: #e9ecef;
            color: #495057;
        }

        .role-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            margin-left: 1rem;
        }

        .role-admin { background: #dc3545; color: white; }
        .role-chef { background: #28a745; color: white; }
        .role-coordinateur { background: #17a2b8; color: white; }
        .role-enseignant { background: #ffc107; color: #212529; }
        .role-vacataire { background: #6f42c1; color: white; }
        .role-etudiant { background: #fd7e14; color: white; }
    </style>
</head>
<body>
    <div class="dashboards-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid p-4">
                <div class="messages-header">
                    <div>
                        <h1>
                            <i class="fas fa-envelope me-3"></i>
                            Messages
                        </h1>
                        <span class="role-badge role-<?php echo str_replace(' ', '-', strtolower($userRole)); ?>">
                            <?php echo ucfirst($userRole); ?>
                        </span>
                    </div>
                    <div class="messages-actions">
                        <?php if (in_array($userRole, ['admin', 'chef de departement'])): ?>
                        <button id="createMessageBtn" class="create-message-btn">
                            <i class="fas fa-plus me-1"></i>
                            Créer un message
                        </button>
                        <?php endif; ?>
                        <button id="markAllBtn" class="mark-all-btn">
                            <i class="fas fa-check-double me-1"></i>
                            Tout marquer comme lu
                        </button>
                    </div>
                </div>

                <div id="notificationsList" class="notifications-list">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                        <p class="mt-2 text-muted">Chargement des messages...</p>
                    </div>
                </div>

                <!-- Pagination will be added here by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Message Creation Modal -->
    <?php if (in_array($userRole, ['admin', 'chef de departement'])): ?>
    <div class="modal fade" id="createMessageModal" tabindex="-1" aria-labelledby="createMessageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createMessageModalLabel">
                        <i class="fas fa-envelope me-2"></i>
                        Créer un nouveau message
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="createMessageForm">
                        <div class="mb-3">
                            <label for="messageTitle" class="form-label">Titre du message</label>
                            <input type="text" class="form-control" id="messageTitle" required>
                        </div>
                        <div class="mb-3">
                            <label for="messageContent" class="form-label">Contenu</label>
                            <textarea class="form-control" id="messageContent" rows="5" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="messageReceiver" class="form-label">Destinataire (optionnel)</label>
                            <select class="form-select" id="messageReceiver">
                                <option value="">Message général (tous les utilisateurs)</option>
                                <!-- Options will be populated by JavaScript -->
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="createMessage()">
                        <i class="fas fa-paper-plane me-1"></i>
                        Envoyer
                    </button>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Messages JS -->
    <script src="../assets/js/messages.js"></script>

    <script>
        // Configuration pour le JavaScript
        const userRole = '<?php echo $userRole; ?>';
        const userId = <?php echo $userId ? $userId : 'null'; ?>;
        const basePath = '../../route/messagesRoute.php';

        // Initialiser la page
        document.addEventListener('DOMContentLoaded', function() {
            loadMessages();
            
            // Event listeners pour les boutons
            const createBtn = document.getElementById('createMessageBtn');
            if (createBtn) {
                createBtn.addEventListener('click', function() {
                    const modal = new bootstrap.Modal(document.getElementById('createMessageModal'));
                    modal.show();
                });
            }

            const markAllBtn = document.getElementById('markAllBtn');
            if (markAllBtn) {
                markAllBtn.addEventListener('click', markAllAsRead);
            }
        });

        // Fonction pour créer un message
        function createMessage() {
            const title = document.getElementById('messageTitle').value.trim();
            const content = document.getElementById('messageContent').value.trim();
            const receiverId = document.getElementById('messageReceiver').value;

            if (!title || !content) {
                alert('Veuillez remplir tous les champs obligatoires.');
                return;
            }

            const messageData = {
                title: title,
                content: content,
                sender_id: userId
            };

            if (receiverId) {
                messageData.receiver_id = receiverId;
            }

            fetch('../../route/messagesRoute.php?action=create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(messageData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Message envoyé avec succès !');
                    const modal = bootstrap.Modal.getInstance(document.getElementById('createMessageModal'));
                    modal.hide();
                    document.getElementById('createMessageForm').reset();
                    loadMessages(); // Recharger les messages
                } else {
                    alert('Erreur lors de l\'envoi du message.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Erreur lors de l\'envoi du message.');
            });
        }

        // Fonction pour marquer tous les messages comme lus
        function markAllAsRead() {
            fetch('../../route/messagesRoute.php?action=markAllAsRead', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadMessages(); // Recharger les messages
                } else {
                    alert('Erreur lors de la mise à jour.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Erreur lors de la mise à jour.');
            });
        }
    </script>
</body>
</html>
