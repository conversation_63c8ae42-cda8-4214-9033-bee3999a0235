<?php
require_once __DIR__ . '/../model/messagesModel.php';
require_once __DIR__ . '/../utils/response.php';

// Set content type to JSON
header('Content-Type: application/json');

// Handle CORS
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, DELETE, PUT");
header("Access-Control-Allow-Headers: Content-Type");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

/**
 * Send group message to teachers below threshold
 */
function sendGroupMessageAPI() {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a department head
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'chef de departement') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    // Validate required parameters
    if (!isset($input['sender_id']) || !isset($input['recipients']) || 
        !isset($input['subject']) || !isset($input['content'])) {
        jsonResponse(['error' => 'Missing required parameters'], 400);
        exit;
    }

    $senderId = $input['sender_id'];
    $recipients = $input['recipients'];
    $subject = $input['subject'];
    $content = $input['content'];
    $academicYear = $input['academic_year'] ?? '';

    // Validate recipients array
    if (!is_array($recipients) || empty($recipients)) {
        jsonResponse(['error' => 'Recipients must be a non-empty array'], 400);
        exit;
    }

    // Validate sender ID
    if (empty($senderId) || !is_numeric($senderId)) {
        jsonResponse(['error' => 'Invalid sender ID'], 400);
        exit;
    }

    // Validate subject and content
    if (empty(trim($subject)) || empty(trim($content))) {
        jsonResponse(['error' => 'Subject and content cannot be empty'], 400);
        exit;
    }

    try {
        $sentCount = 0;
        $errors = [];

        // Send message to each recipient
        foreach ($recipients as $recipientId) {
            if (!is_numeric($recipientId)) {
                $errors[] = "Invalid recipient ID: $recipientId";
                continue;
            }

            // Create message with academic year context in content
            $messageContent = $content;
            if (!empty($academicYear)) {
                $messageContent = "Année académique: $academicYear\n\n" . $content;
            }

            $result = createMessage(
                $senderId,
                $subject,
                $messageContent,
                $recipientId
            );

            if ($result) {
                $sentCount++;
            } else {
                $errors[] = "Failed to send message to recipient ID: $recipientId";
            }
        }

        // Prepare response
        $response = [
            'success' => $sentCount > 0,
            'sent_count' => $sentCount,
            'total_recipients' => count($recipients)
        ];

        if (!empty($errors)) {
            $response['errors'] = $errors;
            $response['partial_success'] = $sentCount > 0 && $sentCount < count($recipients);
        }

        if ($sentCount === 0) {
            $response['error'] = 'Failed to send messages to any recipients';
            jsonResponse($response, 500);
        } else {
            jsonResponse($response, 200);
        }

    } catch (Exception $e) {
        error_log("Error in sendGroupMessageAPI: " . $e->getMessage());
        jsonResponse(['error' => 'Internal server error: ' . $e->getMessage()], 500);
    }
}

/**
 * Get teachers below threshold for a specific department and academic year
 */
function getTeachersBelowThresholdAPI() {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    // Check if user is logged in and is a department head
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'chef de departement') {
        jsonResponse(['error' => 'Unauthorized access'], 401);
        exit;
    }

    $departmentId = $_GET['department_id'] ?? $_SESSION['user']['id_departement'] ?? '';
    $academicYear = $_GET['academic_year'] ?? '';

    if (empty($departmentId)) {
        jsonResponse(['error' => 'Department ID is required'], 400);
        exit;
    }

    if (empty($academicYear)) {
        $currentYear = date('Y');
        $nextYear = $currentYear + 1;
        $academicYear = "$currentYear-$nextYear";
    }

    try {
        // This would require additional model functions to get teachers below threshold
        // For now, return a placeholder response
        jsonResponse([
            'success' => true,
            'data' => [],
            'message' => 'Feature not yet implemented'
        ], 200);

    } catch (Exception $e) {
        error_log("Error in getTeachersBelowThresholdAPI: " . $e->getMessage());
        jsonResponse(['error' => 'Internal server error'], 500);
    }
}

// Handle API requests based on action parameter
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';

    switch ($action) {
        case 'sendGroupMessage':
            sendGroupMessageAPI();
            break;

        default:
            jsonResponse(['error' => 'Invalid action'], 400);
            break;
    }
} elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $action = $_GET['action'] ?? '';

    switch ($action) {
        case 'getTeachersBelowThreshold':
            getTeachersBelowThresholdAPI();
            break;

        default:
            jsonResponse(['error' => 'Invalid action'], 400);
            break;
    }
} else {
    jsonResponse(['error' => 'Method not allowed'], 405);
}
?>
