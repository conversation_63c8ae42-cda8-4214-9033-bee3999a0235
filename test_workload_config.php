<?php
// Test script to create sample workload configurations
require_once 'model/configurationChargeModel.php';

// Get current academic year
$currentYear = date('Y');
$nextYear = $currentYear + 1;
$academicYear = "$currentYear-$nextYear";

echo "Creating test workload configurations for academic year: $academicYear\n\n";

// Create configuration for enseignant
$enseignantConfig = [
    'annee_universitaire' => $academicYear,
    'role' => 'enseignant',
    'charge_minimale' => 180
];

$result1 = createWorkloadConfiguration($enseignantConfig);
if (isset($result1['error'])) {
    echo "Error creating enseignant configuration: " . $result1['error'] . "\n";
} else {
    echo "✓ Created enseignant configuration: 180h minimum\n";
}

// Create configuration for vacataire
$vacataireConfig = [
    'annee_universitaire' => $academicYear,
    'role' => 'vacataire',
    'charge_minimale' => 120
];

$result2 = createWorkloadConfiguration($vacataireConfig);
if (isset($result2['error'])) {
    echo "Error creating vacataire configuration: " . $result2['error'] . "\n";
} else {
    echo "✓ Created vacataire configuration: 120h minimum\n";
}

echo "\nTest configurations created successfully!\n";
echo "You can now test the workload overview page to see the threshold highlighting.\n";
?>
